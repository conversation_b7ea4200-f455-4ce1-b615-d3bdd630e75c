<?php
/**
 * Dynasty Trade Calculator - Membership Class
 *
 * Handles all membership-related functionality including:
 * - Customer and membership retrieval
 * - RotoGPT integration
 * - Membership status transitions
 * - WordPress hooks and actions
 */

namespace DynastyTradeCalculator;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Membership
{
    /**
     * Singleton instance
     */
    private static $instance = null;

    /**
     * Empty constructor - no initialization needed
     */
    public function __construct()
    {
        // Empty constructor as requested
    }

    /**
     * Get singleton instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize membership functionality and hooks
     * Call this method to set up all WordPress hooks and shortcodes
     */
    public function init()
    {
        // Add debug log to confirm init method is called
        error_log('DTC DEBUG: Membership init() method called');

        // Register WordPress hooks
        add_action('rcp_transition_membership_status', [$this, 'handleMembershipStatusTransition'], 10, 3);
        // Run at priority 5 to execute BEFORE RCP's automatic activation (which runs at priority 10)
        add_action('rcp_new_membership_added', [$this, 'handleNewMembershipAdded'], 5, 1);
        // Hook into payment completion to prevent automatic activation of scheduled downgrades
        add_action('rcp_update_payment_status_complete', [$this, 'preventDowngradeActivation'], 5, 1);

        // Disable proration for downgrades only - use the early hook to prevent calculation entirely
        add_filter('rcp_membership_disable_prorate_credit', [$this, 'disableProrationCreditForDowngrades'], 10, 2);
        // Keep the existing filter as backup for edge cases
        add_filter('rcp_membership_get_prorate_credit', [$this, 'disableProrationForDowngrades'], 10, 3);
        // Also hook into the membership level expiration calculation to prevent proration there
        add_filter('rcp_membership_level_get_expiration_length', [$this, 'adjustExpirationLengthForDowngrades'], 10, 3);

        // Hook into the registration initialization to prevent proration fee from being added for downgrades
        add_action('rcp_registration_init', [$this, 'preventDowngradeProrationFee'], 5);

        // Hook into the registration fee addition to filter out proration fees for downgrades
        add_filter('rcp_registration_add_fee', [$this, 'filterRegistrationFees'], 10, 2);

        // Register shortcodes
        add_shortcode('dtc_register_form', [$this, 'registerFormShortcode']);
        add_shortcode('dtc-easy-pricing-table', [$this, 'easyPricingTableShortcode']);

        // Customize proration messages for downgrades
        add_filter('rcp_registration_prorate_message', [$this, 'customizeProrationMessage'], 10, 1);
        add_action('rcp_before_subscription_form_fields', [$this, 'addCustomDowngradeMessage'], 5);

        // Also hook into the earlier action to ensure our message shows before RCP's proration message
        add_action('rcp_before_subscription_form_fields', [$this, 'removeRcpProrationMessageForDowngrades'], 1);

        // Add debugging hook to log registration details
        add_action('rcp_before_subscription_form_fields', [$this, 'debugRegistrationDetails'], 2);

        // Add more debugging hooks to catch different scenarios
        add_action('wp', [$this, 'debugPageLoad'], 1);
        add_action('template_redirect', [$this, 'debugTemplateRedirect'], 1);

        // Hook into shortcode execution
        add_action('rcp_before_register_form', [$this, 'debugBeforeRegisterForm'], 1, 2);
        add_action('rcp_after_register_form', [$this, 'debugAfterRegisterForm'], 1, 2);

        // Fix renewal date display for downgrades
        add_action('rcp_register_total_details_footer_bottom', [$this, 'fixRenewalDateForDowngrades'], 5);

        // Customize expiration date display for pending memberships
        add_filter('rcp_membership_expiration_date', [$this, 'customizeExpirationDateDisplay'], 10, 2);

        // Handle auto-renewal for pending memberships
        add_action('rcp_membership_post_activate', [$this, 'handleAutoRenewalForPendingMemberships'], 10, 2);

        // Handle auto-renewal when pending memberships become active
        add_action('rcp_transition_membership_status', [$this, 'handlePendingToActiveTransition'], 10, 3);

        // Hide pending subscriptions from user interface (handled by JavaScript)

        // Add simple CSS and JS to hide pending memberships
        add_action('wp_footer', [$this, 'addPendingMembershipHidingScript']);



        // Add admin action to audit auto-renewal configuration
        add_action('wp_ajax_dtc_audit_auto_renewal', [$this, 'auditAutoRenewalConfiguration']);
    }
    /**
     * Get the current user's RCP customer object
     *
     * @return \RCP_Customer|null Customer object or null if not found
     */
    public function getCurrentUserCustomer()
    {
        $current_user = wp_get_current_user();
        $current_user_id = $current_user->ID;
        $current_customer = rcp_get_customer_by_user_id($current_user_id);

        return $current_customer;
    }

    /**
     * Get the current user's active or cancelled membership
     *
     * @return \RCP_Membership|null Membership object or null if not found/expired
     */
    public function getCurrentUserMembership()
    {
        $current_customer = $this->getCurrentUserCustomer();
        $current_membership = $current_customer ? ($current_customer->get_memberships(['status' => ['active', 'cancelled']])[0] ?? null) : null;

        // If membership is cancelled, check if it's still valid
        if ($current_membership && $current_membership->get_status() === 'cancelled') {
            $expiration_date = strtotime($current_membership->get_expiration_date(false));
            if ($expiration_date < time()) {
                return null;
            }
        }

        return $current_membership;
    }

    /**
     * Get the current user's membership that was active at a specific date
     *
     * @param string $date Date to check membership for
     * @return \RCP_Membership|null Membership object or null if not found
     */
    public function getCurrentUserMembershipAtDate($date)
    {
        $current_customer = $this->getCurrentUserCustomer();
        if (empty($current_customer)) {
            return [];
        }

        $memberships = $current_customer->get_memberships();
        if (empty($memberships)) {
            return [];
        }
        foreach ($memberships as $membership) {
            if (
                $membership->get_activated_date()
                && $membership->get_expiration_date(false)
                && $membership->get_activated_date() <= $date
                && $membership->get_expiration_date(false) >= $date
            ) {
                return $membership;
            }
        }

        return null;
    }

    /**
     * Check if a customer is invited to the ChatDTC pilot program
     *
     * @param \RCP_Customer $customer Customer object to check
     * @return bool True if invited, false otherwise
     */
    public function isCustomerInvitedToChatdtcPilot($customer)
    {
        $notes = $customer->get_notes();
        $is_invited = strpos($notes, DTC_CHATDTC_PILOT_INVITATION_TEXT) !== false;
        if (!$is_invited) {
            $membership = $customer->get_memberships(['status' => 'active'])[0] ?? null;
            if (
                $membership
                && (
                    $membership->get_recurring_amount() == 2.99
                    || $membership->get_recurring_amount() == 29.99
                )
            ) {
                $is_invited = true;
            }
        }

        return $is_invited;
    }

    /**
     * Check if a customer has lost legacy membership options
     *
     * @param \RCP_Customer $customer Customer object to check
     * @return bool True if legacy options lost, false otherwise
     */
    public function isLegacyMembershipOptionsLost($customer)
    {
        $notes = $customer->get_notes();
        $is_legacy_lost = strpos($notes, DTC_LEGACY_MEMBERSHIP_LOST_NOTE) !== false;

        return $is_legacy_lost;
    }

    /**
     * Check if the current user is invited to the ChatDTC pilot program
     *
     * @return bool True if invited, false otherwise
     */
    public function isCurrentUserInvitedToChatdtcPilot()
    {
        $current_customer = $this->getCurrentUserCustomer();
        if (empty($current_customer)) {
            return false;
        }

        return $this->isCustomerInvitedToChatdtcPilot($current_customer);
    }

    /**
     * Get the RotoGPT subscription type for a membership
     *
     * @param \RCP_Membership $membership Membership object
     * @return string|null RotoGPT subscription type or null if not found
     */
    public function getRotoGptSubscription($membership)
    {
        $rotogpt_subscription = null;
        $is_customer_invited_to_pilot = $this->isCustomerInvitedToChatdtcPilot($membership->get_customer());
        $membership_level = $membership->get_object_id();

        Debug::debug('Membership level in getRotoGptSubscription: ' . $membership_level);
        // Debug::logObject(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE');

        foreach (DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE as $mapping) {
            if ($mapping['membership_level_id'] == $membership_level) {
                Debug::logObject($mapping, 'Found mapping for membership level ' . $membership_level);
                $rotogpt_subscription = $mapping['rotogpt_subscription_type'];
                break;
            }
        }

        if (empty($rotogpt_subscription) && $is_customer_invited_to_pilot && DTC_IS_CHATDTC_PILOT_ACTIVE) {
            $rotogpt_subscription = DTC_ROTOGPT_TEST_DRIVE_SUBSCRIPTION;
        }

        if (empty($rotogpt_subscription) && !DTC_IS_CHATDTC_PILOT_ACTIVE) {
            $rotogpt_subscription = DTC_ROTOGPT_TEST_DRIVE_SUBSCRIPTION;
        }

        return $rotogpt_subscription;
    }

    /**
     * Sign in to RotoGPT and get access token
     *
     * @param \RCP_Membership $membership Membership object
     * @param string $rotogpt_subscription RotoGPT subscription type
     * @return string|false Access token or false on failure
     */
    public function rotoGptSignin($membership, $rotogpt_subscription)
    {
        $rotogpt_signin_request_body = json_encode([
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'client_password' => DTC_ROTOGPT_PASSWORD,
            'current_user' => [
                'user_id' => (string) $membership->get_customer()->get_id(),
                'membership' => $rotogpt_subscription,
                'sign_up_date' => date('d-m-Y H:i:s', strtotime($membership->get_activated_date())),
            ],
        ]);

        $api_endpoint = DTC_IS_PRODUCTION ? 'https://api.rotogpt.com/signin' : 'https://api.dev.rotogpt.com/signin';

        Debug::logApiCall($api_endpoint, json_decode($rotogpt_signin_request_body, true), null, 'Signin');

        $rotogpt_signin_response = wp_remote_post(
            $api_endpoint,
            [
                'body' => $rotogpt_signin_request_body,
                'timeout' => 30,
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]
        );

        Debug::logObject($rotogpt_signin_response, 'DTC RotoGPT Signin: Response');

        // Check for HTTP errors
        if (is_wp_error($rotogpt_signin_response)) {
            Debug::error('DTC RotoGPT Signin Error: HTTP request failed - ' . $rotogpt_signin_response->get_error_message(), true);
            return false;
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($rotogpt_signin_response);
        if ($response_code !== 200) {
            Debug::error('DTC RotoGPT Signin Error: HTTP ' . $response_code, true);
            Debug::error('DTC RotoGPT Signin Response: ' . wp_remote_retrieve_body($rotogpt_signin_response), true);
            return false;
        }

        $rotogpt_signin_response_body = wp_remote_retrieve_body($rotogpt_signin_response);
        $rotogpt_signin_response = json_decode($rotogpt_signin_response_body, true);

        Debug::logObject($rotogpt_signin_response, 'DTC RotoGPT Signin: Response body');

        return $rotogpt_signin_response['accessToken'];
    }

    /**
     * Update an existing RotoGPT subscription for upgrades/downgrades
     *
     * @param \RCP_Membership $membership New membership object
     * @param \RCP_Membership $old_membership Old membership object
     * @param string $rotogpt_subscription_type New RotoGPT subscription type
     * @param string $old_rotogpt_subscription Old RotoGPT subscription type
     * @param string $new_rotogpt_subscription New RotoGPT subscription type
     * @return bool Success status
     */
    public function rotoGptUpdateSubscription($membership, $old_membership, $rotogpt_subscription_type, $old_rotogpt_subscription, $new_rotogpt_subscription) {
        $membership_id = $membership->get_id();

        // Determine if this is an upgrade or downgrade and when to apply the change
        // instead of pricing we need to follow the DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE order
        // e.x. low to high: free, standard_50, standard_100, standard_200, vip, admin
        $is_upgrade = false;
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
        $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
        if ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index) {
            $is_upgrade = true;
        }

        $user_id = $membership->get_customer()->get_id();
        $request = [
            'user_id' => (string) $user_id,
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'subscription_type' => $rotogpt_subscription_type,
        ];

        if ($is_upgrade) {
            // For upgrades, apply immediately
            $request['apply_immediately'] = true;
        } else {
            // For downgrades, apply at the end of the current subscription period
            $request['apply_immediately'] = false;

            // Get the expiration date of the old membership
            $old_expiration = $old_membership->get_expiration_date();
            if (!empty($old_expiration) && $old_expiration !== '0000-00-00 00:00:00') {
                // Format the date for RotoGPT API (assuming they expect ISO format)
                $start_date = date('Y-m-d\TH:i:s\Z', strtotime($old_expiration));
                $request['new_subscription_start_date'] = $start_date;
            } else {
                // If no expiration date, apply immediately as fallback
                $request['apply_immediately'] = true;
                unset($request['new_subscription_start_date']);
            }
        }

        // Use the new membership for signin (it has the current user info)
        $access_token = $this->rotoGptSignin($membership, $new_rotogpt_subscription ?: $old_rotogpt_subscription);

        if (!$access_token) {
            Debug::error('DTC RotoGPT Update Error: Failed to get access token for membership #' . $membership_id, true);
            return false;
        }

        $api_endpoint = DTC_IS_PRODUCTION
            ? 'https://api.rotogpt.com/subscriptions/update'
            : 'https://api.dev.rotogpt.com/subscriptions/update';

        // Debug::logApiCall($api_endpoint, $request, null, 'Update');

        $response = wp_remote_post(
            $api_endpoint,
            [
                'body' => json_encode($request),
                'timeout' => 30,
                'headers' => [
                    'Authorization' => "Bearer {$access_token}",
                    'Content-Type' => 'application/json',
                ],
            ]
        );

        // Check for HTTP errors
        if (is_wp_error($response)) {
            Debug::error('DTC RotoGPT Update Error: HTTP request failed - ' . $response->get_error_message(), true);
            return false;
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            Debug::error('DTC RotoGPT Update Error: HTTP ' . $response_code, true);
            Debug::error('DTC RotoGPT Update Response: ' . wp_remote_retrieve_body($response), true);
            return false;
        }

        Debug::info('DTC RotoGPT Update: Response code: ' . $response_code);
        Debug::logObject(wp_remote_retrieve_body($response), 'DTC RotoGPT Update: Response body');

        // Add note to customer about the RotoGPT subscription update
        $old_level_name = $old_membership->get_membership_level_name();
        $new_level_name = $membership->get_membership_level_name();

        if ($is_upgrade) {
            $note = sprintf(
                'RotoGPT subscription upgraded from %s (%s) to %s (%s) - applied immediately',
                $old_level_name,
                $old_rotogpt_subscription ?: 'none',
                $new_level_name,
                $rotogpt_subscription_type
            );
        } else {
            $schedule_info = isset($request['new_subscription_start_date'])
                ? ' - scheduled for ' . $request['new_subscription_start_date']
                : ' - applied immediately (no expiration date found)';
            $note = sprintf(
                'RotoGPT subscription downgraded from %s (%s) to %s (%s)%s',
                $old_level_name,
                $old_rotogpt_subscription ?: 'none',
                $new_level_name,
                $rotogpt_subscription_type,
                $schedule_info
            );
        }

        $membership->get_customer()->add_note($note);

        $log_message = $is_upgrade
            ? 'DTC RotoGPT Upgrade Success: Updated user #' . $user_id . ' from ' . ($old_rotogpt_subscription ?: 'none') . ' to ' . $rotogpt_subscription_type . ' (immediate)'
            : 'DTC RotoGPT Downgrade Success: Scheduled user #' . $user_id . ' from ' . ($old_rotogpt_subscription ?: 'none') . ' to ' . $rotogpt_subscription_type . (isset($request['new_subscription_start_date']) ? ' starting ' . $request['new_subscription_start_date'] : ' (immediate - no expiration)');

        Debug::info($log_message);
        return true;
    }

    /**
     * Create a new RotoGPT subscription for a new user
     *
     * @param \RCP_Membership $membership Membership object
     * @param string $rotogpt_subscription_type RotoGPT subscription type
     * @return bool Success status
     */
    public function rotoGptCreateSubscription($membership, $rotogpt_subscription_type) {
        $user_id = $membership->get_customer()->get_id();
        Debug::info('DTC RotoGPT Create: Creating subscription for user # (customer id)' . $user_id . ' with type ' . $rotogpt_subscription_type);

        $request = [
            'user_id' => (string) $user_id,
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'subscription_type' => $rotogpt_subscription_type,
        ];

        $api_endpoint = DTC_IS_PRODUCTION
            ? 'https://api.rotogpt.com/subscriptions/create'
            : 'https://api.dev.rotogpt.com/subscriptions/create';

        // Debug::logApiCall($api_endpoint, $request, null, 'Create');

        $response = wp_remote_post(
            $api_endpoint,
            [
                'body' => json_encode($request),
                'timeout' => 30,
                'headers' => [
                    // 'Authorization' => "Bearer {$access_token}", // not needed
                    'Content-Type' => 'application/json',
                ],
            ]
        );

        Debug::logObject($response, 'DTC RotoGPT Create: Response');

        // Check for HTTP errors
        if (is_wp_error($response)) {
            Debug::error('DTC RotoGPT Create Error: HTTP request failed - ' . $response->get_error_message(), true);
            return false;
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            Debug::error('DTC RotoGPT Create Error: HTTP ' . $response_code, true);
            Debug::error('DTC RotoGPT Create Response: ' . wp_remote_retrieve_body($response), true);
            return false;
        }

        // Debug::info('DTC RotoGPT Create: Response code: ' . $response_code);
        // Debug::logObject(wp_remote_retrieve_body($response), 'DTC RotoGPT Create: Response body');

        // Add note to customer about the RotoGPT subscription creation
        $level_name = $membership->get_membership_level_name();
        $note = sprintf(
            'RotoGPT subscription created: %s (%s)',
            $level_name,
            $rotogpt_subscription_type
        );
        $membership->get_customer()->add_note($note);

        Debug::info('DTC RotoGPT Create Success: Created subscription for user #' . $user_id . ' with type ' . $rotogpt_subscription_type);
        return true;
    }

    /**
     * Handle membership status transitions - specifically when memberships expire
     * This function cancels the RotoGPT account when a membership expires
     * ENHANCED: Also handles scheduled downgrade activation
     */
    public function handleMembershipStatusTransition($old_status, $new_status, $membership_id)
    {
        Debug::info('DTC Membership Status Transition: Membership #' . $membership_id . ' transitioned from ' . $old_status . ' to ' . $new_status);
        // Do we need bottom codes if new_status is not "expired"?
        $membership = rcp_get_membership($membership_id);
        $customer = $membership->get_customer();
        $customer->add_note(DTC_LEGACY_MEMBERSHIP_LOST_NOTE);

        $rogogpt_subscription = $this->getRotoGptSubscription($membership);
        if (empty($rogogpt_subscription)) {
            return;
        }
        
        if ($new_status == 'expired') {
            $pending_memberships = $customer->get_memberships(['status' => 'pending']);
            $pending_downgrade_id = is_array($pending_memberships) && count($pending_memberships) ? $pending_memberships[0]->get_id() : null;
            
            if (!empty($pending_downgrade_id)) {
                Debug::info('DTC Downgrade Activation: Expired membership #' . $membership_id . ' has pending downgrade to #' . $pending_downgrade_id);

                $pending_membership = rcp_get_membership($pending_downgrade_id);
                if ($pending_membership && $pending_membership->get_status() === 'pending') {
                    $pending_membership->set_status('active');

                    $pending_membership->add_note('Scheduled downgrade activated - membership is now active');
                    $membership->add_note('Expired - scheduled downgrade to membership #' . $pending_downgrade_id . ' has been activated');

                    Debug::info('DTC Downgrade Activation: Successfully activated pending membership #' . $pending_downgrade_id);

                    // We dont need this as we already scheduled pending membership.
                    // Update RotoGPT for the newly activated membership;
                    // $new_rotogpt_subscription = $this->getRotoGptSubscription($pending_membership);
                    // if (!empty($new_rotogpt_subscription)) {
                    //     $this->rotoGptUpdateSubscription($pending_membership, $membership, $new_rotogpt_subscription, $rogogpt_subscription, $new_rotogpt_subscription);
                    //     Debug::info('DTC Downgrade Activation: Updated RotoGPT subscription for newly activated membership #' . $pending_downgrade_id);
                    // }
                }

                return;
            }
        } else {
            // Handle other status transitions if needed
            return;
        }

        // bottom code working?
        $access_token = $this->rotoGptSignin($membership, $rogogpt_subscription);

        $user_id = $membership->get_customer()->get_id();
        $request = [
            'user_id' => (string) $user_id,
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'subscription_type' => 'cancelled_account',
            'apply_immediately' => true,
        ];
        $response = wp_remote_post(
            DTC_IS_PRODUCTION
                ? 'https://api.rotogpt.com//subscriptions/update'
                : 'https://api.dev.rotogpt.com/subscriptions/update',
                // : 'https://api.dev.rotogpt.com/cancel_account', // old enddpoint
            [
                'body' => json_encode($request),
                'timeout' => 30,
                'headers' => [
                    'Authorization' => "Bearer {$access_token}",
                    'Content-Type' => 'application/json',
                ],
            ]
        );
        $response_body = wp_remote_retrieve_body($response);
        $response = json_decode($response_body, true);
    }

    /**
     * Prevent automatic activation of scheduled downgrades when payment is completed
     * This runs BEFORE rcp_complete_registration to prevent unwanted activation
     */
    public function preventDowngradeActivation($payment_id)
    {
        global $rcp_payments_db;

        $payment = $rcp_payments_db->get_payment($payment_id);
        if (empty($payment) || empty($payment->membership_id)) {
            return;
        }

        $membership = rcp_get_membership($payment->membership_id);
        if (empty($membership)) {
            return;
        }

        // Check if this is a downgrade that should be scheduled
        if ($membership->was_upgrade()) {
            $old_membership_id = $membership->get_upgraded_from();
            $old_membership = rcp_get_membership($old_membership_id);

            if (!empty($old_membership)) {
                $old_rotogpt_subscription = $this->getRotoGptSubscription($old_membership);
                $new_rotogpt_subscription = $this->getRotoGptSubscription($membership);

                // Determine if this is an upgrade or downgrade: Custom LOGIC - if subscription_type similar, then we skip this.
                if ($old_rotogpt_subscription === $new_rotogpt_subscription) {
                    return;
                }
                
                // Determine if this is a downgrade
                $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
                $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
                $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
                $is_upgrade = ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index);

                if (!$is_upgrade) {
                    $old_expiration = $old_membership->get_expiration_date();

                    if (!empty($old_expiration) && $old_expiration !== '0000-00-00 00:00:00') {
                        Debug::info('DTC Downgrade Fix: Preventing automatic activation of scheduled downgrade membership #' . $membership->get_id());

                        // Temporarily remove the rcp_complete_registration hook to prevent activation
                        remove_action('rcp_update_payment_status_complete', 'rcp_complete_registration', 10);

                        // Add our custom completion handler that skips activation
                        add_action('rcp_update_payment_status_complete', [$this, 'completeDowngradeRegistrationWithoutActivation'], 10, 1);
                    }
                }
            }
        }
    }

    /**
     * Custom registration completion for downgrades that skips activation
     */
    public function completeDowngradeRegistrationWithoutActivation($payment_id)
    {
        // Re-add the original hook for future payments
        add_action('rcp_update_payment_status_complete', 'rcp_complete_registration', 10);

        // Remove our custom hook
        remove_action('rcp_update_payment_status_complete', [$this, 'completeDowngradeRegistrationWithoutActivation'], 10);

        Debug::info('DTC Downgrade Fix: Completed downgrade registration without activation for payment #' . $payment_id);
    }

    /**
     * Prevent proration fee from being added during registration for downgrades
     * This hooks into the registration initialization before the proration fee is calculated
     *
     * @param \RCP_Registration $registration The registration object
     * @return void
     */
    public function preventDowngradeProrationFee($registration)
    {
        // Only process upgrade/downgrade registrations
        if (!in_array($registration->get_registration_type(), ['upgrade', 'downgrade'])) {
            return;
        }

        // Get the current membership
        $membership = $registration->get_membership();
        if (!$membership) {
            return;
        }

        // Get the target membership level ID from the registration
        $target_level_id = $registration->get_membership_level_id();
        if (!$target_level_id) {
            return;
        }

        // Determine if this is a downgrade by comparing subscription types
        $current_rotogpt_subscription = $this->getRotoGptSubscription($membership);

        // Find the target subscription type
        $target_rotogpt_subscription = null;
        foreach (DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE as $mapping) {
            if ($mapping['membership_level_id'] == $target_level_id) {
                $target_rotogpt_subscription = $mapping['rotogpt_subscription_type'];
                break;
            }
        }

        if (!$target_rotogpt_subscription || $current_rotogpt_subscription === $target_rotogpt_subscription) {
            return;
        }

        // Determine if this is a downgrade based on subscription type order
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $current_index = array_search($current_rotogpt_subscription, $rotogpt_subscriptions);
        $target_index = array_search($target_rotogpt_subscription, $rotogpt_subscriptions);
        $is_upgrade = ($target_index > $current_index);

        if (!$is_upgrade) {
            // This is a downgrade - remove the default proration fee hook and add our custom one
            remove_action('rcp_registration_init', 'rcp_add_prorate_fee');
            add_action('rcp_registration_init', [$this, 'addCustomProrateFeeForDowngrades'], 10);

            Debug::info('DTC Registration: Prevented proration fee for downgrade from ' . $current_rotogpt_subscription . ' to ' . $target_rotogpt_subscription);
        }
    }

    /**
     * Custom proration fee handler that skips proration for downgrades
     * This replaces the default RCP proration fee function for downgrades
     *
     * @param \RCP_Registration $registration The registration object
     * @return void
     */
    public function addCustomProrateFeeForDowngrades($registration)
    {
        // If this isn't an upgrade/downgrade, don't process
        if (!in_array($registration->get_registration_type(), ['upgrade', 'downgrade'])) {
            return;
        }

        // For downgrades, we don't add any proration fee (charge full price)
        Debug::info('DTC Registration: Skipping proration fee addition for downgrade - charging full price');

        // Note: We don't add any fee here, which means the user pays the full subscription price
        // The existing membership will remain active until its expiration date
    }

    /**
     * Filter registration fees to remove proration credits for downgrades
     * This is called when fees are added to the registration object
     *
     * @param array $fee The fee array being added
     * @param \RCP_Registration $registration The registration object
     * @return array|false Modified fee array or false to prevent adding the fee
     */
    public function filterRegistrationFees($fee, $registration)
    {

        // Only process proration fees
        if (!isset($fee['proration']) || !$fee['proration']) {
            return $fee;
        }

        // Only process upgrade/downgrade registrations
        if (!in_array($registration->get_registration_type(), ['upgrade', 'downgrade'])) {
            return $fee;
        }

        // Get the current membership
        $membership = $registration->get_membership();
        if (!$membership) {
            return $fee;
        }

        // Get the target membership level ID from the registration
        $target_level_id = $registration->get_membership_level_id();
        if (!$target_level_id) {
            return $fee;
        }

        // Determine if this is a downgrade by comparing subscription types
        $current_rotogpt_subscription = $this->getRotoGptSubscription($membership);

        // Find the target subscription type
        $target_rotogpt_subscription = null;
        foreach (DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE as $mapping) {
            if ($mapping['membership_level_id'] == $target_level_id) {
                $target_rotogpt_subscription = $mapping['rotogpt_subscription_type'];
                break;
            }
        }

        // If target subscription is not mapped, treat it as a downgrade from any ChatDTC membership
        // This handles cases where users downgrade from ChatDTC to regular DTC memberships
        if (!$target_rotogpt_subscription) {
            // If current membership has ChatDTC and target doesn't, it's a downgrade
            if ($current_rotogpt_subscription && $current_rotogpt_subscription !== 'free') {
                Debug::info('DTC Registration: Blocking proration fee for downgrade from ChatDTC (' . $current_rotogpt_subscription . ') to regular DTC membership (level ' . $target_level_id . ') - fee amount: ' . $fee['amount']);
                return false; // Return false to prevent the fee from being added
            }
            // If current is also not mapped or is free, allow normal processing
            return $fee;
        }

        if ($current_rotogpt_subscription === $target_rotogpt_subscription) {
            return $fee;
        }

        // Determine if this is a downgrade based on subscription type order
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $current_index = array_search($current_rotogpt_subscription, $rotogpt_subscriptions);
        $target_index = array_search($target_rotogpt_subscription, $rotogpt_subscriptions);
        $is_upgrade = ($target_index > $current_index);

        if (!$is_upgrade) {
            // This is a downgrade - prevent the proration fee from being added
            Debug::info('DTC Registration: Blocking proration fee for downgrade from ' . $current_rotogpt_subscription . ' to ' . $target_rotogpt_subscription . ' - fee amount: ' . $fee['amount']);
            return false; // Return false to prevent the fee from being added
        }

        // This is an upgrade - allow the proration fee
        Debug::info('DTC Registration: Allowing proration fee for upgrade from ' . $current_rotogpt_subscription . ' to ' . $target_rotogpt_subscription);
        return $fee;
    }

    /**
     * Disable proration credit calculation entirely for downgrades
     * This is the early hook that prevents any proration calculation from happening
     *
     * @param bool $disable_credit Whether to disable proration credit
     * @param \RCP_Membership $membership The membership object
     * @return bool True to disable proration for downgrades, false otherwise
     */
    public function disableProrationCreditForDowngrades($disable_credit, $membership)
    {
        // Only process if this is an upgrade/downgrade scenario
        if (!$membership->was_upgrade()) {
            return $disable_credit;
        }

        $old_membership_id = $membership->get_upgraded_from();
        $old_membership = rcp_get_membership($old_membership_id);

        if (empty($old_membership)) {
            return $disable_credit;
        }

        // Get subscription types for comparison
        $old_rotogpt_subscription = $this->getRotoGptSubscription($old_membership);
        $new_rotogpt_subscription = $this->getRotoGptSubscription($membership);

        if ($old_rotogpt_subscription === $new_rotogpt_subscription) {
            return $disable_credit;
        }

        // Determine if this is a downgrade based on subscription type order
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
        $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
        $is_upgrade = ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index);

        if (!$is_upgrade) {
            // This is a downgrade - disable proration entirely
            Debug::info('DTC Proration: Disabling proration credit calculation for downgrade from ' . $old_rotogpt_subscription . ' to ' . $new_rotogpt_subscription . ' (membership #' . $membership->get_id() . ')');
            return true;
        }

        // This is an upgrade - allow normal proration
        Debug::info('DTC Proration: Allowing proration credit calculation for upgrade from ' . $old_rotogpt_subscription . ' to ' . $new_rotogpt_subscription);
        return $disable_credit;
    }

    /**
     * Disable proration for downgrades only (backup filter)
     * This prevents RCP from extending the expiration date of downgraded memberships
     *
     * @param float $discount The proration credit amount
     * @param int $membership_id The membership ID
     * @param \RCP_Membership $membership The membership object
     * @return float Modified discount amount (0 for downgrades, original for upgrades)
     */
    public function disableProrationForDowngrades($discount, $membership_id, $membership)
    {
        // Only process if this is an upgrade/downgrade scenario
        if (!$membership->was_upgrade()) {
            return $discount;
        }

        $old_membership_id = $membership->get_upgraded_from();
        $old_membership = rcp_get_membership($old_membership_id);

        if (empty($old_membership)) {
            return $discount;
        }

        // Get RotoGPT subscription types to determine if this is a downgrade
        $old_rotogpt_subscription = $this->getRotoGptSubscription($old_membership);
        $new_rotogpt_subscription = $this->getRotoGptSubscription($membership);

        // If same subscription type, allow normal proration
        if ($old_rotogpt_subscription === $new_rotogpt_subscription) {
            return $discount;
        }

        // Determine if this is a downgrade based on subscription type order
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
        $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
        $is_upgrade = ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index);

        if (!$is_upgrade) {
            // This is a downgrade - disable proration
            Debug::info('DTC Proration: Disabling proration for downgrade from ' . $old_rotogpt_subscription . ' to ' . $new_rotogpt_subscription . ' (membership #' . $membership_id . ')');
            return 0;
        }

        // This is an upgrade - allow normal proration
        Debug::info('DTC Proration: Allowing proration for upgrade from ' . $old_rotogpt_subscription . ' to ' . $new_rotogpt_subscription . ' (membership #' . $membership_id . ')');
        return $discount;
    }

    /**
     * Adjust expiration length for downgrades to prevent proration extension
     * This ensures downgraded memberships get the standard duration, not extended by proration
     *
     * @param int $expiration_length The calculated expiration length
     * @param int $membership_level_id The membership level ID
     * @param int $upgraded_from The ID of the membership being upgraded from
     * @return int Modified expiration length
     */
    public function adjustExpirationLengthForDowngrades($expiration_length, $membership_level_id, $upgraded_from)
    {
        // Only process if this is an upgrade/downgrade scenario
        if (empty($upgraded_from)) {
            return $expiration_length;
        }

        $old_membership = rcp_get_membership($upgraded_from);
        if (empty($old_membership)) {
            return $expiration_length;
        }

        // Create a temporary membership object to check subscription types
        $membership_level = rcp_get_membership_level($membership_level_id);
        if (empty($membership_level)) {
            return $expiration_length;
        }

        // Get RotoGPT subscription types
        $old_rotogpt_subscription = $this->getRotoGptSubscription($old_membership);

        // For the new subscription, we need to look it up by membership level
        $new_rotogpt_subscription = null;
        foreach (DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE as $mapping) {
            if ($mapping['membership_level_id'] == $membership_level_id) {
                $new_rotogpt_subscription = $mapping['rotogpt_subscription_type'];
                break;
            }
        }

        if (empty($new_rotogpt_subscription)) {
            return $expiration_length;
        }

        // If same subscription type, allow normal proration
        if ($old_rotogpt_subscription === $new_rotogpt_subscription) {
            return $expiration_length;
        }

        // Determine if this is a downgrade based on subscription type order
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
        $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
        $is_upgrade = ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index);

        if (!$is_upgrade) {
            // This is a downgrade - return the standard membership level duration without proration
            $standard_length = $membership_level->get_duration();
            Debug::info('DTC Proration: Adjusting expiration length for downgrade from ' . $old_rotogpt_subscription . ' to ' . $new_rotogpt_subscription . ' - using standard length: ' . $standard_length . ' instead of prorated: ' . $expiration_length);
            return $standard_length;
        }

        // This is an upgrade - allow normal proration
        Debug::info('DTC Proration: Allowing normal expiration length for upgrade from ' . $old_rotogpt_subscription . ' to ' . $new_rotogpt_subscription);
        return $expiration_length;
    }

    /**
     * Handle new memberships and membership upgrades/downgrades
     * This function creates or updates RotoGPT subscriptions when membership changes occur
     */
    public function handleNewMembershipAdded($membership_id)
    {
        Debug::info('DTC RotoGPT: New membership added - membership ID: ' . $membership_id);
        
        $membership = rcp_get_membership($membership_id);
        $customer = $membership->get_customer();

        Debug::logMembership('Processing', $membership_id, [
            'membership_level' => $membership->get_object_id(),
            'customer_id' => $customer->get_id(),
            'membership_status' => $membership->get_status(),
            'is_upgrade' => $membership->was_upgrade(),
        ]);

        // Check if this is an upgrade/downgrade or a brand new membership
        $is_upgrade_downgrade = $membership->was_upgrade();

        // If same subscription_type (free) and RCP says upgrade (based on price), then we follow RCP.
        if ($is_upgrade_downgrade) {
            Debug::info('DTC RotoGPT: Membership #' . $membership_id . ' is an upgrade/downgrade - using update endpoint');

            // Get the old membership that was upgraded/downgraded from
            $old_membership_id = $membership->get_upgraded_from();
            $old_membership = rcp_get_membership($old_membership_id);

            if (empty($old_membership)) {
                Debug::error('DTC RotoGPT Update Error: Could not find old membership #' . $old_membership_id, true);
                return;
            }

            $old_rotogpt_subscription = $this->getRotoGptSubscription($old_membership);
            $new_rotogpt_subscription = $this->getRotoGptSubscription($membership);

            // Determine if this is an upgrade or downgrade: Custom LOGIC - if subscription_type similar, then we skip this.
            if ($old_rotogpt_subscription === $new_rotogpt_subscription) {
                Debug::info('DTC RotoGPT: Membership #' . $membership_id . ' is same subscription type - skipping');
                return;
            }

            $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
            $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
            $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
            $is_upgrade = ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index);

            if (!$is_upgrade) {
                Debug::info('DTC Downgrade Fix: Detected downgrade - preserving current membership until expiration');

                $old_expiration = $old_membership->get_expiration_date();

                if (!empty($old_expiration) && $old_expiration !== '0000-00-00 00:00:00') {
                    Debug::info('DTC Downgrade Fix: Preserving expiration date: ' . $old_expiration);

                    // Calculate the proper expiration date for the new membership
                    $membership_level = rcp_get_membership_level($membership->get_object_id());
                    $new_expiration = date('Y-m-d H:i:s', strtotime($old_expiration . ' + ' . $membership_level->get_duration() . ' ' . $membership_level->get_duration_unit()));

                    Debug::info('DTC Downgrade Fix: New membership will expire on: ' . $new_expiration);

                    $old_membership->set_status('active');
                    $membership->update([
                        'status' => 'pending',
                        'created_date' => $old_expiration, // Will activate when old expires
                        'activated_date' => $old_expiration,
                        'expiration_date' => $new_expiration // Set proper expiration date
                    ]);

                    $old_membership->add_note(sprintf(
                        'Downgrade scheduled: Will change to %s (Level %d) on %s, expires %s',
                        $membership->get_membership_level_name(),
                        $membership->get_object_id(),
                        date('Y-m-d', strtotime($old_expiration)),
                        date('Y-m-d', strtotime($new_expiration))
                    ));

                    $membership->add_note(sprintf(
                        'Scheduled downgrade from %s (Level %d) - will activate on %s, expires %s',
                        $old_membership->get_membership_level_name(),
                        $old_membership->get_object_id(),
                        date('Y-m-d', strtotime($old_expiration)),
                        date('Y-m-d', strtotime($new_expiration))
                    ));

                    Debug::info('DTC Downgrade Fix: Successfully scheduled downgrade for ' . $old_expiration);

                    // For scheduled downgrades, we're done - the WordPress membership scheduling is complete
                    // return;
                } else {
                    Debug::info('DTC Downgrade Fix: No expiration date found, proceeding with immediate downgrade');
                }
            }
            
            // Only proceed if the new membership has a valid RotoGPT subscription
            if (empty($new_rotogpt_subscription)) {
                Debug::error('DTC RotoGPT Update Error: No RotoGPT subscription type found for new membership level', true);
                return;
            }

            // Call the update function
            $success = $this->rotoGptUpdateSubscription(
                $membership,
                $old_membership,
                $new_rotogpt_subscription,
                $old_rotogpt_subscription,
                $new_rotogpt_subscription
            );

            if (!$success) {
                Debug::error('DTC RotoGPT Update Error: Failed to update RotoGPT subscription for membership #' . $membership_id, true);
            }

        } else {
            Debug::info('DTC RotoGPT: Membership #' . $membership_id . ' is a new membership - using create endpoint');

            // This is a brand new membership, create a new RotoGPT subscription
            $rotogpt_subscription_type = $this->getRotoGptSubscription($membership);

            if (empty($rotogpt_subscription_type)) {
                Debug::error('DTC RotoGPT Create Error: No RotoGPT subscription type found for membership level', true);
                return;
            }

            // Call the create function
            $success = $this->rotoGptCreateSubscription($membership, $rotogpt_subscription_type);

            if (!$success) {
                Debug::error('DTC RotoGPT Create Error: Failed to create RotoGPT subscription for membership #' . $membership_id, true);
            }
        }
    }

    /**
     * Registration form shortcode
     * Displays available membership levels based on user's current status and eligibility
     */
    public function registerFormShortcode($atts = [])
    {
        $membership = $this->getCurrentUserMembershipAtDate(DTC_LEGACY_MEMBERSHIP_REFERENCE_DATE);
        $is_legacy_lost = $membership ? $this->isLegacyMembershipOptionsLost($membership->get_customer()) : true;
        $current_membership = $this->getCurrentUserMembership();
        $membership_levels = [];

        foreach (DTC_MEMBERSHIP_LEVEL_RULES as $rule) {
            $membership_level = $GLOBALS['rcp_levels_db']->get_level($rule['membership_level_id']);
            if ($current_membership && $current_membership->get_recurring_amount() == $membership_level->price) {
                continue;
            }
            if (
                $rule['valid_for_users_that_paid_any_of_these_amounts_only']
                && (
                    empty($membership)
                    || $is_legacy_lost
                    || !in_array($membership->get_recurring_amount(), $rule['valid_for_users_that_paid_any_of_these_amounts_only'])
                )
            ) {
                continue;
            }
            if (
                $rule['is_chatdtc_membership']
                && DTC_IS_CHATDTC_PILOT_ACTIVE
                && !$this->isCurrentUserInvitedToChatdtcPilot()
            ) {
                continue;
            }
            $membership_levels[] = $rule['membership_level_id'];
        }

        return do_shortcode('[register_form ids="' . implode(',', $membership_levels) . '"]');
    }

    /**
     * Easy pricing table shortcode
     * Displays the appropriate pricing table based on user's status and pilot eligibility
     */
    public function easyPricingTableShortcode($atts = [])
    {
        Debug::logObject($atts, 'Easy Pricing Table Shortcode Attributes');
        $membership = $this->getCurrentUserMembershipAtDate(DTC_LEGACY_MEMBERSHIP_REFERENCE_DATE);
        $is_legacy_lost = $membership ? $this->isLegacyMembershipOptionsLost($membership->get_customer()) : true;
        $is_current_user_invited = $this->isCurrentUserInvitedToChatdtcPilot();

        foreach (DTC_PRICING_TABLE_RULES as $rule) {
            if (
                $rule['is_chatdtc_table']
                && DTC_IS_CHATDTC_PILOT_ACTIVE
                && !$is_current_user_invited
            ) {
                continue;
            }
            if (
                !$rule['is_chatdtc_table']
                && !DTC_IS_CHATDTC_PILOT_ACTIVE
            ) {
                continue;
            }
            if (
                !$rule['is_chatdtc_table']
                && $is_current_user_invited
            ) {
                continue;
            }
            if (
                $rule['valid_for_users_that_paid_any_of_these_amounts_only']
                && (
                    empty($membership)
                    || $is_legacy_lost
                    || !in_array($membership->get_recurring_amount(), $rule['valid_for_users_that_paid_any_of_these_amounts_only'])
                )
            ) {
                continue;
            }
            $pricing_table_id = $rule['pricing_table_id'];
            break;
        }

        Debug::info('DTC Pricing Table: Displaying table #' . $pricing_table_id);
        return do_shortcode('[easy-pricing-table id="' . $pricing_table_id . '"]');
    }

    /**
     * Customize proration message for downgrades
     * Hide proration amounts for downgrades and show appropriate messaging
     *
     * @param string $message The original proration message
     * @return string Modified message or empty string to hide
     */
    public function customizeProrationMessage($message)
    {
        // Use our custom downgrade detection instead of relying on RCP's registration type
        if ($this->isCurrentRegistrationADowngrade()) {
            Debug::info('DTC Proration: Hiding proration message for detected downgrade');
            return ''; // Hide proration message for downgrades
        }

        return $message; // Keep original message for upgrades
    }

    /**
     * Add custom message for downgrades explaining tier activation timing
     * This replaces the confusing proration message with clear information
     */
    public function addCustomDowngradeMessage()
    {
        error_log('DTC DEBUG: addCustomDowngradeMessage() called');

        // Use our custom downgrade detection instead of relying on RCP's registration type
        if (!$this->isCurrentRegistrationADowngrade()) {
            error_log('DTC DEBUG: Not a downgrade, not showing custom message');
            return; // Only show for downgrades
        }

        error_log('DTC DEBUG: This is a downgrade, showing custom message');

        $registration = rcp_get_registration();
        if (!$registration) {
            error_log('DTC DEBUG: No registration object in addCustomDowngradeMessage');
            return;
        }

        // Try to get current membership from registration first
        $current_membership = $registration->get_membership();

        // If not found, get user's actual membership (same logic as in isCurrentRegistrationADowngrade)
        if (!$current_membership) {
            $current_user = wp_get_current_user();
            if ($current_user->ID) {
                $customer = rcp_get_customer_by_user_id($current_user->ID);
                if ($customer) {
                    $current_membership = rcp_get_customer_single_membership($customer->get_id());
                    error_log('DTC DEBUG: Found user current membership for message: ' . ($current_membership ? $current_membership->get_id() : 'none'));
                }
            }
        }

        if (!$current_membership) {
            error_log('DTC DEBUG: No current membership found for custom message');
            return;
        }

        $expiration_date = $current_membership->get_expiration_date();
        if (empty($expiration_date) || $expiration_date === '0000-00-00 00:00:00') {
            error_log('DTC DEBUG: No valid expiration date: ' . $expiration_date);
            return;
        }

        $message = sprintf(
            '<div class="rcp-downgrade-notice" style="background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 15px 0; border-radius: 4px;"><p><strong>%s</strong> %s</p></div>',
            __('Downgrade Information:', 'rcp'),
            sprintf(
                __('Your new membership tier will activate after your current subscription expires on %s. You will continue to have access to your current tier until then.', 'rcp'),
                date_i18n(get_option('date_format'), strtotime($expiration_date))
            )
        );

        error_log('DTC DEBUG: Displaying custom downgrade message');
        Debug::info('DTC Downgrade: Displaying custom downgrade message');
        echo $message;
    }

    /**
     * Customize expiration date display for pending memberships
     * Show correct expiration dates based on custom downgrade logic
     *
     * @param string $expiration_date The formatted expiration date
     * @param RCP_Membership $membership The membership object
     * @return string Modified expiration date display
     */
    public function customizeExpirationDateDisplay($expiration_date, $membership)
    {
        // Only customize for pending memberships
        if ($membership->get_status() !== 'pending') {
            return $expiration_date;
        }

        // Check if this is a scheduled downgrade by looking for an active membership from the same customer
        $customer = $membership->get_customer();
        $active_memberships = $customer->get_memberships(['status' => 'active']);

        if (empty($active_memberships)) {
            return $expiration_date; // No active membership, show normal expiration
        }

        $active_membership = $active_memberships[0];
        $active_expiration = $active_membership->get_expiration_date(false);

        if (empty($active_expiration) || $active_expiration === '0000-00-00 00:00:00') {
            return $expiration_date; // No active expiration, show normal expiration
        }

        // For pending downgrades, show when the new tier will actually activate
        $activation_message = sprintf(
            __('Activates %s (after current tier expires)', 'rcp'),
            date_i18n(get_option('date_format'), strtotime($active_expiration))
        );

        return $activation_message;
    }

    /**
     * Handle auto-renewal for pending memberships
     * Ensures proper auto-renewal configuration when memberships are created
     *
     * @param int $membership_id The ID of the membership being activated
     * @param array $args Additional arguments
     */
    public function handleAutoRenewalForPendingMemberships($membership_id, $args = [])
    {
        $membership = rcp_get_membership($membership_id);
        if (!$membership) {
            return;
        }

        // If this is a pending membership, configure auto-renewal properly
        if ($membership->get_status() === 'pending') {
            // Enable auto-renewal for ALL pending memberships
            $membership->set_recurring(true);

            // Disable auto-renewal on current active membership to prevent conflicts
            $customer = $membership->get_customer();
            if ($customer) {
                $active_memberships = $customer->get_memberships(['status' => 'active']);
                foreach ($active_memberships as $active_membership) {
                    if ($active_membership->get_id() !== $membership_id && $active_membership->is_recurring()) {
                        $active_membership->set_recurring(false);
                        $customer->add_note('Auto-renewal disabled on current membership due to pending membership scheduled.');
                    }
                }
            }

            $customer->add_note('Pending membership created with auto-renewal enabled for seamless transition.');
        }
    }

    /**
     * Handle auto-renewal when pending memberships transition to active
     * Ensures auto-renewal stays enabled for seamless renewals and old memberships stay disabled
     */
    public function handlePendingToActiveTransition($old_status, $new_status, $membership_id)
    {
        // Only handle pending to active transitions
        if ($old_status !== 'pending' || $new_status !== 'active') {
            return;
        }

        $membership = rcp_get_membership($membership_id);
        if (!$membership) {
            return;
        }

        // Ensure auto-renewal stays enabled for the newly activated membership
        if (!$membership->is_recurring()) {
            $membership->set_recurring(true);
        }

        $customer = $membership->get_customer();

        // Ensure any other active memberships (that shouldn't exist but just in case)
        // have auto-renewal disabled to prevent conflicts
        $all_memberships = $customer->get_memberships(['status' => ['active', 'cancelled']]);
        foreach ($all_memberships as $other_membership) {
            if ($other_membership->get_id() !== $membership_id &&
                $other_membership->get_status() === 'active' &&
                $other_membership->is_recurring()) {
                $other_membership->set_recurring(false);
                Debug::info('Auto-renewal disabled on old active membership #' . $other_membership->get_id() . ' when new membership #' . $membership_id . ' became active');
            }
        }

        $customer->add_note('Pending membership activated with auto-renewal enabled for continuous service.');
        Debug::info('Pending membership #' . $membership_id . ' activated with auto-renewal enabled');
    }



    /**
     * Detect if the current registration is a downgrade based on our subscription tier hierarchy
     * This is more accurate than RCP's price-based detection for our tier system
     *
     * @return bool True if current registration is a downgrade, false otherwise
     */
    public function isCurrentRegistrationADowngrade()
    {
        error_log('DTC DEBUG: isCurrentRegistrationADowngrade() called');

        $registration = rcp_get_registration();
        if (!$registration) {
            error_log('DTC DEBUG: No registration object in isCurrentRegistrationADowngrade');
            return false;
        }

        // Get current membership and target membership level from registration
        $current_membership = $registration->get_membership();
        $target_level_id = $registration->get_membership_level_id();

        error_log('DTC DEBUG: Target level ID from registration: ' . $target_level_id);
        error_log('DTC DEBUG: Current membership from registration exists: ' . ($current_membership ? 'yes' : 'no'));

        // If RCP registration doesn't have proper data, try to get user's actual membership
        if (!$current_membership) {
            $current_user = wp_get_current_user();
            if ($current_user->ID) {
                $customer = rcp_get_customer_by_user_id($current_user->ID);
                if ($customer) {
                    $current_membership = rcp_get_customer_single_membership($customer->get_id());
                    error_log('DTC DEBUG: Found user current membership: ' . ($current_membership ? $current_membership->get_id() : 'none'));
                }
            }
        }

        // If we still don't have a current membership, this isn't a downgrade
        if (!$current_membership) {
            error_log('DTC DEBUG: No current membership found, not a downgrade');
            return false;
        }

        // If target level ID is 0 or empty, check if this is a registration form with multiple levels
        // In this case, we need to determine if any of the available levels would be a downgrade
        if (!$target_level_id || $target_level_id == 0) {
            error_log('DTC DEBUG: No target level from registration, checking URL parameters and form context');

            // Check if registration_type=upgrade in URL suggests this should be treated as potential downgrade
            $registration_type_param = $_REQUEST['registration_type'] ?? '';
            if ($registration_type_param === 'upgrade') {
                error_log('DTC DEBUG: URL indicates upgrade intent, treating as potential downgrade scenario');
                // For now, return true to trigger our custom messaging
                // TODO: Could be enhanced to check specific levels available in the form
                return true;
            }

            error_log('DTC DEBUG: No clear downgrade indicators');
            return false;
        }

        // Get subscription types for comparison
        $current_subscription_type = $this->getRotoGptSubscription($current_membership);

        // Find target subscription type
        $target_subscription_type = null;
        foreach (DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE as $mapping) {
            if ($mapping['membership_level_id'] == $target_level_id) {
                $target_subscription_type = $mapping['rotogpt_subscription_type'];
                break;
            }
        }

        // If target is not mapped, treat as free (which could be a downgrade)
        if (!$target_subscription_type) {
            $target_subscription_type = DTC_ROTOGPT_TEST_DRIVE_SUBSCRIPTION; // 'free'
        }

        error_log('DTC DEBUG: Current subscription type: ' . $current_subscription_type);
        error_log('DTC DEBUG: Target subscription type: ' . $target_subscription_type);

        // Compare subscription types using our tier hierarchy
        $subscription_hierarchy = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $current_index = array_search($current_subscription_type, $subscription_hierarchy);
        $target_index = array_search($target_subscription_type, $subscription_hierarchy);

        // If either subscription type is not found in hierarchy, fall back to 'free' (index 0)
        if ($current_index === false) {
            $current_index = 0; // Treat as free
        }
        if ($target_index === false) {
            $target_index = 0; // Treat as free
        }

        $is_downgrade = ($target_index < $current_index);

        Debug::info('DTC Downgrade Detection: Current=' . $current_subscription_type . ' (index=' . $current_index . '), Target=' . $target_subscription_type . ' (index=' . $target_index . '), IsDowngrade=' . ($is_downgrade ? 'yes' : 'no'));

        return $is_downgrade;
    }

    /**
     * Remove RCP's proration message action for downgrades
     * This prevents the original proration message from showing at all for downgrades
     */
    public function removeRcpProrationMessageForDowngrades()
    {
        Debug::debug('removeRcpProrationMessageForDowngrades() called');

        if ($this->isCurrentRegistrationADowngrade()) {
            // Remove RCP's proration message action for downgrades
            remove_action('rcp_before_subscription_form_fields', 'rcp_add_prorate_message');
            Debug::info('DTC Proration: Removed RCP proration message action for downgrade');
        } else {
            Debug::debug('Not a downgrade, keeping RCP proration message');
        }
    }

    /**
     * Debug method to log registration details for troubleshooting
     */
    public function debugRegistrationDetails()
    {
        Debug::debug('debugRegistrationDetails() called');

        // Log URL parameters
        Debug::debug('URL Parameters - registration_type: ' . ($_REQUEST['registration_type'] ?? 'not set') . ', membership_id: ' . ($_REQUEST['membership_id'] ?? 'not set'));

        // Check if user is logged in
        $current_user = wp_get_current_user();
        Debug::debug('User logged in: ' . ($current_user->ID ? 'yes (ID: ' . $current_user->ID . ')' : 'no'));

        // Check user's actual memberships
        if ($current_user->ID) {
            $customer = rcp_get_customer_by_user_id($current_user->ID);
            if ($customer) {
                $memberships = $customer->get_memberships();
                Debug::debug('User has ' . count($memberships) . ' memberships');
                foreach ($memberships as $membership) {
                    Debug::debug('Membership ID: ' . $membership->get_id() . ', Level: ' . $membership->get_object_id() . ', Status: ' . $membership->get_status());
                }

                // Check if the membership_id from URL exists
                $target_membership_id = $_REQUEST['membership_id'] ?? null;
                if ($target_membership_id) {
                    $target_membership = rcp_get_membership($target_membership_id);
                    if ($target_membership) {
                        Debug::debug('Target membership exists - ID: ' . $target_membership->get_id() . ', User: ' . $target_membership->get_user_id() . ', Level: ' . $target_membership->get_object_id() . ', Status: ' . $target_membership->get_status());
                    } else {
                        Debug::debug('Target membership ID ' . $target_membership_id . ' does not exist');
                    }
                }
            } else {
                Debug::debug('User has no RCP customer record');
            }
        }

        $registration = rcp_get_registration();
        if (!$registration) {
            Debug::info('DTC Debug: No registration object found');
            return;
        }

        $current_membership = $registration->get_membership();
        $target_level_id = $registration->get_membership_level_id();
        $registration_type = $registration->get_registration_type();

        Debug::info('DTC Debug: Registration Details - Type: ' . $registration_type . ', Target Level: ' . $target_level_id);

        if ($current_membership) {
            Debug::info('DTC Debug: Current Membership - Level: ' . $current_membership->get_object_id() . ', Status: ' . $current_membership->get_status());
        } else {
            Debug::info('DTC Debug: No current membership found');
        }

        $is_downgrade = $this->isCurrentRegistrationADowngrade();
        Debug::info('DTC Debug: Is Downgrade: ' . ($is_downgrade ? 'YES' : 'NO'));
    }

    /**
     * Debug method to log page load details
     */
    public function debugPageLoad()
    {
        global $wp;
        $current_url = home_url($wp->request);
        Debug::debug('Page load - URL: ' . $current_url);

        if (strpos($current_url, 'signup') !== false) {
            Debug::debug('This is a signup page');

            // Check if RCP registration object exists
            if (function_exists('rcp_get_registration')) {
                $registration = rcp_get_registration();
                if ($registration) {
                    Debug::debug('RCP registration object exists');
                } else {
                    Debug::debug('RCP registration object does not exist yet');
                }
            }
        }
    }

    /**
     * Debug method to log template redirect
     */
    public function debugTemplateRedirect()
    {
        global $wp;
        $current_url = home_url($wp->request);

        if (strpos($current_url, 'signup') !== false) {
            error_log('DTC DEBUG: Template redirect on signup page');
        }
    }

    /**
     * Debug method for before register form hook
     */
    public function debugBeforeRegisterForm($id, $atts)
    {
        error_log('DTC DEBUG: rcp_before_register_form hook fired - ID: ' . $id . ', Atts: ' . print_r($atts, true));

        // Now check registration object
        $registration = rcp_get_registration();
        if ($registration) {
            error_log('DTC DEBUG: Registration object exists in before_register_form');
            $this->debugRegistrationDetails();
        } else {
            error_log('DTC DEBUG: No registration object in before_register_form');
        }
    }

    /**
     * Debug method for after register form hook
     */
    public function debugAfterRegisterForm($id, $atts)
    {
        error_log('DTC DEBUG: rcp_after_register_form hook fired - ID: ' . $id . ', Atts: ' . print_r($atts, true));
    }

    /**
     * Fix renewal date display for downgrades
     * Shows current membership expiration date instead of calculated renewal date
     */
    public function fixRenewalDateForDowngrades()
    {
        error_log('DTC DEBUG: fixRenewalDateForDowngrades() called');

        // Check if this is a downgrade scenario
        if (!$this->isCurrentRegistrationADowngrade()) {
            error_log('DTC DEBUG: Not a downgrade, keeping default renewal date');
            return;
        }

        error_log('DTC DEBUG: This is a downgrade, fixing renewal date display');

        // Remove RCP's default renewal date display
        remove_action('rcp_register_total_details_footer_bottom', 'rcp_add_membership_renewal_date_to_total_details');

        // Get the user's current membership
        $current_user = wp_get_current_user();
        if (!$current_user->ID) {
            error_log('DTC DEBUG: No current user for renewal date fix');
            return;
        }

        $customer = rcp_get_customer_by_user_id($current_user->ID);
        if (!$customer) {
            error_log('DTC DEBUG: No customer found for renewal date fix');
            return;
        }

        $current_membership = rcp_get_customer_single_membership($customer->get_id());
        if (!$current_membership) {
            error_log('DTC DEBUG: No current membership found for renewal date fix');
            return;
        }

        $expiration_date = $current_membership->get_expiration_date();
        if (empty($expiration_date) || $expiration_date === '0000-00-00 00:00:00') {
            error_log('DTC DEBUG: No valid expiration date for renewal date fix');
            return;
        }

        $formatted_date = date_i18n(get_option('date_format'), strtotime($expiration_date));
        error_log('DTC DEBUG: Displaying custom renewal date: ' . $expiration_date . ' (formatted: ' . $formatted_date . ')');

        // Display our custom renewal date row
        ?>
        <tr class="row rcp-renewal-date">
            <td class="cell" scope="col"><?php _e('Next Renewal Due', 'rcp'); ?></td>
            <td class="cell" data-th="<?php esc_attr_e('Next Renewal Due', 'rcp'); ?>">
                <?php echo date_i18n(get_option('date_format'), strtotime($expiration_date)); ?>
                <small style="display: block; color: #666; font-style: italic;">
                    <?php _e('(Current tier expires, new tier activates)', 'rcp'); ?>
                </small>
            </td>
        </tr>
        <?php
    }







    /**
     * Add simple script to hide pending memberships on subscription page
     */
    public function addPendingMembershipHidingScript()
    {
        // Only run on subscription page
        if (!$this->isSubscriptionPage()) {
            return;
        }

        // Get first pending membership info for display
        $firstPendingInfo = $this->getFirstPendingMembershipInfo();
        ?>
        <style>
        .dtc-pending-membership { display: none !important; }
        .dtc-pending-explanation {
            font-style: italic;
            color: #666;
            margin-top: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 4px solid #ddd;
        }
        </style>
        <script>
        jQuery(document).ready(function($) {
            var hiddenCount = 0;

            // Find and hide pending membership rows
            $('#rcp-account-overview tr').each(function() {
                var $row = $(this);
                if ($row.find('td').text().indexOf('Pending') !== -1) {
                    $row.addClass('dtc-pending-membership');
                    hiddenCount++;
                }
            });

            // If there are pending memberships, hide auto-renewal controls from active memberships
            if (hiddenCount > 0) {
                // Hide "(renews automatically)" text when pending memberships exist
                $('.rcp-membership-auto-renew-notice').hide();

                // Hide auto-renewal toggle buttons when pending memberships exist
                // This prevents confusing UX where users can disable auto-renewal on memberships that will expire soon
                $('.rcp-auto-renew-toggle').hide();

                // Add explanation with first pending membership info
                var explanationText = '<?php echo esc_js($firstPendingInfo); ?>';
                $('#rcp-account-overview').after('<p class="dtc-pending-explanation">' + explanationText + '</p>');
            }
        });
        </script>
        <?php
    }

    /**
     * Check if current page is subscription page
     */
    private function isSubscriptionPage()
    {
        global $rcp_options;
        $subscription_page_id = isset($rcp_options['subscription_page']) ? $rcp_options['subscription_page'] : 0;
        $current_url = $_SERVER['REQUEST_URI'] ?? '';

        return ($subscription_page_id && is_page($subscription_page_id)) ||
               strpos($current_url, 'subscription') !== false;
    }

    /**
     * Get first pending membership info for display
     */
    private function getFirstPendingMembershipInfo()
    {
        $customer = rcp_get_customer();
        if (!$customer) {
            return '';
        }

        $memberships = $customer->get_memberships();
        $firstPending = null;
        $activeMembership = null;

        foreach ($memberships as $membership) {
            if ($membership->get_status() === 'pending') {
                $firstPending = $membership;
            } elseif ($membership->get_status() === 'active') {
                $activeMembership = $membership;
            }
        }

        if (!$firstPending || !$activeMembership) {
            return '';
        }

        $levelName = esc_html($firstPending->get_membership_level_name());

        // Calculate activation date as day after current membership expires
        $currentExpiration = $activeMembership->get_expiration_date();
        if ($currentExpiration && $currentExpiration !== 'none') {
            $activationDate = date('F j, Y', strtotime($currentExpiration . ' +1 day'));
        } else {
            $activationDate = esc_html($firstPending->get_expiration_date());
        }

        return "Note: Your {$levelName} membership is scheduled to activate on {$activationDate}.";
    }

    /**
     * Audit auto-renewal configuration for all membership tiers
     * Verifies that all paid tiers have auto-renewal enabled by default
     */
    public function auditAutoRenewalConfiguration()
    {
        Debug::info('Starting auto-renewal configuration audit');

        // Get global auto-renewal behavior
        $auto_renew_behavior = rcp_get_auto_renew_behavior();
        Debug::info('Global auto-renewal behavior: ' . $auto_renew_behavior . ' (1=always, 2=never, 3=customer choice)');

        // Check each of our membership tiers
        $tier_levels = [7, 9, 12]; // $4.99, $6.99, $9.99 tiers

        foreach ($tier_levels as $level_id) {
            $level = rcp_get_membership_level($level_id);
            if ($level) {
                $price = $level->get_price();
                $is_free = $level->is_free();
                $is_lifetime = $level->is_lifetime();

                Debug::info('Level ' . $level_id . ' (' . $level->get_name() . ') - Price: $' . $price . ', Free: ' . ($is_free ? 'yes' : 'no') . ', Lifetime: ' . ($is_lifetime ? 'yes' : 'no'));

                // Check if auto-renewal should be enabled for this level
                $should_auto_renew = !$is_free && !$is_lifetime && $price > 0;
                Debug::info('Level ' . $level_id . ' should auto-renew: ' . ($should_auto_renew ? 'yes' : 'no'));
            } else {
                Debug::warning('Level ' . $level_id . ' not found');
            }
        }

        // Check current user's memberships and their auto-renewal status
        $current_user = wp_get_current_user();
        if ($current_user->ID) {
            $customer = rcp_get_customer_by_user_id($current_user->ID);
            if ($customer) {
                $memberships = $customer->get_memberships();
                foreach ($memberships as $membership) {
                    $auto_renew = $membership->is_recurring();
                    Debug::info('Membership ' . $membership->get_id() . ' (Level: ' . $membership->get_object_id() . ', Status: ' . $membership->get_status() . ') - Auto-renew: ' . ($auto_renew ? 'yes' : 'no'));
                }
            }
        }
    }

    //
}
